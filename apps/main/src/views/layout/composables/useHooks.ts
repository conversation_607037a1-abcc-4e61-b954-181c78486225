import type { ComputedRef } from 'vue';
import { Watermark } from '@boss/design';
import { formatDate } from '@crm/exam-utils';
import { ref, watch, nextTick } from 'vue';
import { useMonitorStore } from '@/store/use-monitor-store';
import { useBroadcastChannel, useIsMobile } from '@crm/exam-hooks';
import { useRoute, useRouter } from 'vue-router';
import { STUDENT_BROADCAST_CHANNEL } from '@crm/exam-constants';
import { ExamStatusEnum } from '@crm/exam-types';

// 类型定义
export type TExamIdCheckResult = 'NotChecked' | 'Valid' | 'Invalid';

// 水印管理
const useWatermark = () => {
    const destroyFn = ref();

    function renderWatermark(userName: string, userId: string) {
        const content = `${userName}-${userId}-${formatDate(new Date(), 'yyyy-MM-dd')}`;

        try {
            // @ts-ignore
            const { destroy } = Watermark.init({
                opacity: 0.1,
                rotate: -10,
                content,
            });
            destroyFn.value = destroy;
        } catch (error) {}
    }

    function clearWatermark() {
        if (destroyFn.value) {
            try {
                destroyFn.value();
                destroyFn.value = null;
            } catch (error) {}
        }
    }

    return { renderWatermark, clearWatermark };
};

// 路由导航管理
const useExamNavigation = () => {
    const $route = useRoute();
    const $router = useRouter();
    const { query, params } = $route;

    const { isMobileView } = useIsMobile();

    // 加密大场次id
    const seqId = computed(() => params?.seqId || query?.seqId || '') as ComputedRef<string>;
    // 加密小考试id
    const examId = computed(() => params?.examId || query?.examId || '') as ComputedRef<string>;

    const logout = () => {
        $router.replace(`/login?seqId=${seqId.value}`);
    };

    const handleExamNavigation = (res: any) => {
        // 只有小场次id 校验不通过 则跳转至考试列表页
        // !examId.value 表示用户之前没有选过考试
        // res.code === 108 表示小场次id 校验不通过
        // 兜底逻辑  确保出现异常时，跳转至考试列表页
        if (!examId.value || res.code === 108) {
            $router.replace(`/exam-list/${seqId.value}`);
        } else if (examId.value) {
            if (isMobileView.value) {
                $router.replace({
                    path: `/status/${seqId.value}`,
                    query: {
                        status: ExamStatusEnum.兼容拦截,
                        text: '请用电脑端登录考试',
                    },
                });
            } else {
                $router.replace(`/monitor?seqId=${seqId.value}&examId=${examId.value}`);
            }
        } else {
            $router.replace(`/exam-list/${seqId.value}`);
        }
    };

    return { logout, handleExamNavigation };
};

export function useLayoutHooks() {
    const $route = useRoute();
    const { path, query, params } = $route;
    const examIdCheckResult = ref<TExamIdCheckResult>('NotChecked');

    const monitorStore = useMonitorStore();
    const { isMobileView } = useIsMobile();
    const { renderWatermark, clearWatermark } = useWatermark();
    const { logout, handleExamNavigation } = useExamNavigation();

    // 加密大场次id
    const seqId = computed(() => params?.seqId || query?.seqId || '') as ComputedRef<string>;
    // 加密小考试id
    const examId = computed(() => params?.examId || query?.examId || '') as ComputedRef<string>;

    watch(
        () => examIdCheckResult.value,
        () => {
            if (examIdCheckResult.value === 'Valid') {
                nextTick(() => {
                    const { userName, encryptUserId } = monitorStore.examBaseInfo;
                    renderWatermark(userName, encryptUserId);
                });
            } else {
                clearWatermark();
            }
        },
    );

    // 接受其他tab页签的通知，退出登陆，保持单会话窗口
    const { data } = useBroadcastChannel(STUDENT_BROADCAST_CHANNEL);
    watch(data, () => {
        if (data.value && path !== '/login') {
            // 理论上进入该入口的path一定不是/login，先保留历史代码
            logout();
        }
    });

    async function getBaseInfo() {
        try {
            const res = await monitorStore.fetchBaseInfo({
                seqId: seqId.value,
                examId: examId.value,
            });

            examIdCheckResult.value = 'Valid';

            if (res.code === 0) {
                handleExamNavigation(res);
            } else {
                examIdCheckResult.value = 'Invalid';
                logout();
            }
        } catch (error) {
            examIdCheckResult.value = 'Invalid';
        }
    }

    // 验证考试id 是否正确
    if (seqId.value) {
        getBaseInfo();
    }

    onUnmounted(() => {
        clearWatermark();
    });

    return { renderWatermark, clearWatermark, logout, examIdCheckResult, isMobileView, examId, seqId };
}
