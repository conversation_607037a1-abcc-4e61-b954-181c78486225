<template>
    <div
        :class="{
            [theme]: !!theme,
            disabled: disabled,
            'is-active-effect': isActiveEffect,
        }"
        class="button-wrap"
    >
        <div class="inner">
            <div class="filter" />
            <div class="text">
                <slot />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
const props = defineProps({
    isActiveEffect: {
        // 是否添加点击效果，默认无
        type: Boolean,
        default: false,
    },
    theme: {
        // 主题色
        type: String,
        default: '',
    },
    disabled: {
        type: <PERSON><PERSON><PERSON>,
        default: false,
    },
});
</script>

<style lang="less" scoped>
.button-wrap {
    position: relative;
    min-width: 90px;
    height: 39px;
    padding-bottom: 3px;
    padding-right: 1px;
    background: linear-gradient(179.99998deg, #e1f512 0%, #399800 100%);
    box-shadow: inset 0 -1px 1.84px 0 #fff9d8;
    border: 1px solid #67a418;
    border-radius: 29.53px;
    overflow: hidden;
    transition: all 0.4s;
    // filter: brightness(108%);

    .inner {
        position: relative;
        height: 100%;
        background: linear-gradient(180deg, #d7ea0b 0%, #55bf14 100%);
        box-shadow: inset 0 -16px 3px 0 #77ff1d;
        border-radius: 31.34px;

        .filter {
            position: relative;
            height: 100%;
            background: linear-gradient(180deg, #d7f113 1%, #4aa800 100%);
            filter: blur(2.3px);
            border-radius: 31.34px;
        }

        &::before {
            position: absolute;
            top: 1%;
            left: 10%;
            content: '';
            width: 77px;
            height: 8px;
            background: rgba(255, 255, 255, 1);
            filter: blur(3px);
            opacity: 0.7;
            z-index: 2;
            border-radius: 50%;
        }

        &::after {
            position: absolute;
            top: 2px;
            left: calc(10% + 77px);
            content: '';
            width: 12px;
            height: 8px;
            transform: matrix(0.9, 0.35, -0.4, 1, 0, 0);
            background: rgba(255, 255, 255, 1);
            filter: blur(3px);
            opacity: 0.7;
            z-index: 2;
            border-radius: 100%;
        }
    }

    .text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translateX(-50%) translateY(-50%);
        color: #f8ffdc;
        font-size: 21px;
        font-style: normal;
        font-weight: 700;
        line-height: 23.64px;
        z-index: 4;
        white-space: nowrap;
        // box-shadow: 0px 0.92px 0.92px 0px #419C02;
        // -webkit-text-stroke: 1px #419C02;
        text-shadow: 0 0 1px #419c02;
    }

    // 禁用-置灰
    &.disabled {
        border-color: #88d675;

        &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            background: #fff;
            opacity: 0.28;
        }

        &.orange {
            border-color: #ffbd9c;
        }
    }

    // 点击动效
    &.is-active-effect {
        cursor: pointer;

        &:active {
            padding-bottom: 0;
            padding-right: 0;
            box-shadow: none;
        }
    }

    // 橘色主题
    &.orange {
        background: linear-gradient(179.99998deg, #e97120 0%, #e97120 100%);
        box-shadow: inset 0 -1px 1.84px 0 #fff9d8;
        border: 1px solid #c04f00;

        .inner {
            background: linear-gradient(180deg, #fff319 0%, #feff67 100%);
            box-shadow: inset 0 -16px 3px 0 rgba(255, 240, 200, 0.2);

            .filter {
                background: linear-gradient(180deg, rgba(252, 226, 10, 0.06) 1%, #ff7c00 100%);
            }
        }

        .text {
            text-shadow: 0 0 1px #c04f00 !important;
        }
    }

    // 红色主题
    &.red {
        background: linear-gradient(180deg, #fe6b46 0%, #e51c11 100%);
        box-shadow: inset 0 -16px 3px 0 #ffd9d8;
        border: 1px solid #e55711;

        .inner {
            background: #ffc3c3;
            box-shadow: inset 0 -16px 3px 0 #ffbcbc;

            .filter {
                background: linear-gradient(180deg, #ffa77d 1%, #ff3202 100%);
            }
        }

        .text {
            text-shadow: 0 0 1px #fc7313 !important;
        }
    }

    // 深红色主题
    &.red-deep {
        background: linear-gradient(179.99998deg, #fe6b46 0%, #cd1341 100%);
        box-shadow: inset 0px -0.92px 1.84px 0px #ffd9d8;
        border: 0.92px solid #e55711;

        .inner {
            background: #ffc3ff;
            box-shadow: inset 0px -16.59px 3.69px 0px #ffbcbc;
            border: 0.92px solid #e55711;

            .filter {
                background: linear-gradient(179.99998deg, #ff94a7 1%, #db2323 100%);
            }
        }

        .text {
            text-shadow: 0 0 1px #be6047 !important;
        }
    }

    // 深橘色主题
    &.orange-deep {
        background: linear-gradient(180deg, #fed646 0%, #e55711 100%);
        box-shadow: inset 0 -1px 1.84px 0 #fff9d8;
        border: 1px solid #e55711;

        .inner {
            background: linear-gradient(180deg, #fed645 0%, #ffecbc 100%);
            box-shadow: inset 0 -16px 3px 0 #fecd5e;

            .filter {
                background: linear-gradient(180deg, #ffcd40 1%, #ff6202 100%);
            }
        }

        .text {
            text-shadow: 0 0 1px #c04f00 !important;
        }
    }

    // 深绿色主题
    &.green-deep {
        height: 47px;
        background: linear-gradient(179.99998deg, #e1f512 0%, #218825 100%);
        box-shadow: inset 0px -0.92px 1.84px 0px #fff9d8;
        border: 0.92px solid #61b44a;
        border-radius: 31.34px;

        .inner {
            height: 41px;
            background: linear-gradient(179.99998deg, #7dea0b 0%, #6ec853 100%);
            box-shadow: inset 0px -16.59px 3.69px 0px #77ff1d;
            border-radius: 31.34px;

            &::before {
                width: 144px;
                height: 10px;
            }
            .filter {
                height: 40px;
                background: linear-gradient(179.99998deg, #c3fe9c 1%, #46a629 100%);
                filter: blur(2.3px);
                border-radius: 31.34px;
            }
        }

        .text {
            text-shadow: 0 0 2px #1d4511 !important;
            color: #fff;
        }
    }
    &.orange-red {
        height: 47px;
        background: linear-gradient(179.99998deg, #fc950a 0%, #db5819 100%);
        box-shadow: inset 0px -0.97px 1.94px 0px #ffffff;
        border: 0.97px solid #df5e04;
        border-radius: 32.91px;

        .inner {
            height: 41px;
            background: #ffb188;
            box-shadow: inset 0px -17.42px 3.87px 0px #ffecbc;
            border-radius: 32.91px;

            &::before {
                display: none;
            }
            &::after {
                left: calc(~'10% + 9px');
                transform: matrix(1.9, -0.4, 0.7, 0.7, 0, 0);
                top: 5px;
            }
            .filter {
                height: 40px;
                background: linear-gradient(179.99998deg, #ffbe89 0%, #f54b00 100%);
                filter: blur(2.2px);
                border-radius: 29.53px;
            }
        }

        .text {
            text-shadow: 0 0 2px #521c01 !important;
            color: #fff;
        }
    }
}
</style>
