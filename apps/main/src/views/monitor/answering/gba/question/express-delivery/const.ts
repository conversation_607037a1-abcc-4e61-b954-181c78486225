export function defineCabinetList() {
    return Array.from({ length: 20 })
        .fill(0)
        .map((item, index) => {
            return {
                index,
                key: index,
                isShow: false,
                showType: '',
            };
        });
}

export function defineRoundData() {
    return {
        encSnapshotId: '', // 快照id
        showList: [], // 需要展示的快递
        selectList: [], // 用户选择的数据
        difficulty: 3, // 难度
        difficultyStage: 1, // 当前难度阶段
        roundStage: 1, // 阶段  1: 记忆阶段  2: 复现阶段 用于匹配 roundStageMap 字段
        isChoose: false, // 是否可以作答
        errNum: 0, // 回答错误次数
        profit: 0, // 可获得奖励,
        repeatTimeTs: 0, // 复现开始时间戳
        timeoutTs: 0, // 作答超时 为0正常作答  大于0超时 值为超时时间搓
        showTimeTs: 0, // 展示时间，开始记忆时间
        repeatDetail: {
            // 复现详情
            chooseTimeTs: [], // 用户选择的时间戳
        },
        result: 0, // 结果  0:失败  1:成功
    };
}

export const roundStageMap = {
    1: {
        text: '记忆阶段',
        color: '#DE4600',
        tips: '记忆阶段',
    },
    2: {
        text: '复现阶段',
        color: '#389900',
        tips: '请复现顺序与位置',
    },
};
