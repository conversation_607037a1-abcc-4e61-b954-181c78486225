<template>
    <div class="round-content">
        <div class="title-wrap">
            <p class="title">{{ numberToChinese(gbaStore.currentIndex + 1, false) }}、{{ gbaStore.currentQuestion.showName }}</p>
            <div class="progress-wrap">
                <Progress :passed="roundPassed" :total="roundList.length" color="linear-gradient(179.99998deg, #B2F56F 0%, #4FC435 11%, #AEE929 45%, #4FC435 92%, #B2F56F 100%)" />
            </div>
            <div class="gold-wrap">
                <p class="gold-icon" />
                <Button>
                    <span>金币:</span>
                    <NumberVue :value="goldNumber.num" :reservedDecimalPlaces="1" />
                </Button>
            </div>
        </div>
        <div class="mine-wrap">
            <div
                v-for="(item, index) in fruitList"
                :key="index"
                class="mine-item"
                :class="{
                    marked: !!item?.marked,
                }"
            >
                <div class="mine-item-inner">
                    <div
                        v-if="item?.bgImg"
                        :style="{
                            background: `url(${item.bgImg})`,
                            // background: `url(${testImg[index]})`,
                            backgroundSize: 'auto 100%',
                            width: '100%',
                            height: '100%',
                        }"
                    />
                </div>
                <div class="marked-tag-wrap">
                    <div
                        class="marked-tag"
                        :class="{
                            show: item?.markedShow,
                        }"
                    >
                        <span class="icon" />
                    </div>
                </div>
                <div class="result-info-wrap">
                    <div v-if="item?.status && item?.status === 1" class="result-info-inner">
                        <p class="text">+¥{{ item.reward }}</p>
                    </div>
                    <div v-else-if="item?.status === 2" class="error result-info-inner">
                        <p class="error-icon" />
                        <p class="text">回答错误</p>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="optionList.length" class="optin-button-wrap">
            <div :class="['optin-button-inner', { 'show-option': showOption }]">
                <template v-for="(item, index) in optionList" :key="index">
                    <div :class="['button-inner', { err: item.status }]">
                        <Button :isActiveEffect="true" :theme="item.status === 2 ? 'red' : item.shortcutKeys === 2 ? 'orange-deep' : ''" @click="onSelectFruit(item)">
                            <div class="button-inner-text">
                                <span v-if="item.status === 0">{{ item.content }}</span>
                                <div
                                    v-else
                                    class="icon"
                                    :class="{
                                        'green-tick': item.status === 1 && item.shortcutKeys === 1,
                                        'orange-tick': item.status === 1 && item.shortcutKeys === 2,
                                        cross: item.status === 2,
                                    }"
                                />
                            </div>
                        </Button>
                        <p
                            class="key-down-desc"
                            :style="{
                                opacity: currentShowRound <= 3 ? 1 : 0,
                            }"
                        >
                            快捷键"{{ item.shortcutKeys }}"
                        </p>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { createTimer, timeCenter } from '@/store/time';
import { ref, computed, onMounted, reactive, nextTick } from 'vue';
import { onKeyStroke } from '@crm/vueuse-pro';

import { numberToChinese } from '@/utils/number-to-chinese';
import { sum } from 'lodash-es';
import NumberVue from '../../component/animation-number.vue';
import Button from '../../component/button.vue';
import { useNotification } from '../../component/notification-tips/index';
import Progress from '../../component/progress.vue';
import { useGbaStore } from '../../store';
import { usePathAnimation } from '../mine/usePathAnimation';

const props = defineProps({
    isSimulate: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(['tips', 'roundOver', 'gameOver', 'roundChange']);
const { startAnimation } = usePathAnimation();
const useNotificationTips = useNotification();
const gbaStore = useGbaStore();

const roundList = computed(() => {
    if (props.isSimulate) {
        // 模拟模式下 使用模拟数据
        return gbaStore.currentQuestion.simulateList;
    } else {
        return gbaStore.currentQuestion.roundList;
    }
});

function defineFruitList() {
    const arr = Array.from({ length: 4 }).map((item) => {
        return {
            markedShow: false,
            status: 0, // 0 未选择  1:已选择结果正确  2:已选择结果错误
            bgImg: '',
        };
    });

    return arr;
}

const fruitList = ref<any>(defineFruitList()); // 水果列表
const optionList = ref<Array<any>>([]); // 按钮列表
const showOption = ref(false); // 按钮是否展示

const markedShowTime = 1.1 * 1000; // 标记展示时长;
const roundTime = gbaStore.currentQuestion.chooseTime; // 每回合时长;

const roundPassed = ref(0); // 已处理回合数
const isChoose = ref(false); // 是否可以作答
const currentShowRound = ref(0);

const goldNumber = reactive({
    num: gbaStore.currentQuestion.reward,
});

function defaultSelectData() {
    return {
        encSnapshotId: '', // 当前回合id
        paramId: 0, // 用户选择的数据（矿山id）
        chooseTimeTs: 0, // 用户选择的时间点
        markTimeTs: 0, // 水果标记结束时间点
        showTimeTs: 0, // 当前回合首次展示的时间
        timeoutTs: 0, // 当前回合超时时间点 (与 chooseTime 互斥);
        x: 0, // 金币飞入起始坐标x
        y: 0, // 金币飞入起始坐标y
        profit: 0, // 当前获得的金币
    };
}

// 缓存已选择的坐标，金币飞入使用
const selectData = ref(defaultSelectData());

const { currentTime } = timeCenter;

// 水果标记 时间控制
const markedShowCountdown = createTimer();

// 是否选择 时间控制
const roundCountdown = createTimer();

// 回合结果展示 时间控制
const roundResultCountdown = createTimer();

// 选择水果
function onSelectFruit(data: any) {
    // 如果不在可选择时间内 禁止选择
    if (!isChoose.value) {
        return;
    }

    isChoose.value = false;

    // 停止回合时间
    roundCountdown?.stop();

    // 设置已选择的数据
    const newSelectData = {
        ...selectData.value,
        chooseTimeTs: currentTime.value,
        profit: data.reward, // 每回合的金额
        paramId: data.paramId,
    };

    const newStatus = data.correct === 1 ? 1 : 2;

    // 同步按钮状态
    data.status = newStatus;

    // 同步水果状态（水果翻转）
    const fruitIndex = fruitList.value.findIndex((item: { paramId: any }) => {
        return item?.paramId && item.paramId === data.paramId;
    });

    if (fruitIndex >= 0) {
        fruitList.value[fruitIndex] = {
            ...fruitList.value[fruitIndex],
            status: newStatus,
            reward: data.reward,
        };
    }

    if (data.reward) {
        const endElements = document.querySelectorAll('.mine-item');
        const endElement = endElements[fruitIndex];
        const endElementBound = endElement!.getBoundingClientRect();

        newSelectData.x = endElementBound!.left + endElementBound!.width / 2;
        newSelectData.y = endElementBound!.top + endElementBound!.height / 2;
    }

    selectData.value = newSelectData;
    roundResultCountdown?.start({
        key: 'roundResultCountdown',
        finishTime: () => Date.now() + 1.5 * 1000,
        onFinished: () => {
            roundEnd();
        },
    });
}

// 快捷键选择
onKeyStroke(['1', '2'], (event) => {
    const key = event.key; // 获取按下的键名
    const data = optionList.value.find((item) => `${item.shortcutKeys}` === key);
    onSelectFruit(data);
});

// 回合结束
function roundEnd() {
    roundPassed.value += 1;
    emits('roundOver', { ...selectData.value });

    if (selectData.value.x && selectData.value.y) {
        const endElement = document.querySelector('.gold-wrap');
        const endElementBound = endElement!.getBoundingClientRect();

        startAnimation(
            [
                {
                    size: [100, 50],
                    position: [selectData.value.x, selectData.value.y],
                    opacity: 1,
                },
                {
                    size: [100, 50],
                    position: [(endElementBound.left + selectData.value.x + 20) / 2, (endElementBound.top + (selectData.value.y + 20) * 2) / 3],
                    opacity: 0.75,
                },
                {
                    size: [100, 50],
                    position: [endElementBound.left + 20, endElementBound.top + 20],
                    opacity: 0.5,
                },
            ],
            () => {
                goldNumber.num += selectData.value.profit;
                roundContinue();
            },
        );
    } else {
        roundContinue();
    }
}

// 继续下一个回合
function roundContinue() {
    currentShowRound.value += 1; // 记录本次答题已展示回合

    // 回合结束-游戏结束
    if (roundPassed.value >= roundList.value.length) {
        emits('roundChange', { type: 'end' });
        gameOver();
        return;
    }

    const roundData = roundList.value[roundPassed.value];
    emits('roundChange', { currentQuestionId: roundData.encryptId });

    nextTick(() => {
        setRoundData();
        markedShowCountdown.start({
            key: 'markedShowCountdown',
            finishTime: (t) => t + markedShowTime,
            onFinished: () => {
                // 展示标记
                const data = fruitList.value.find((item: { marked: number }) => item?.marked && item?.marked === 1);
                data.markedShow = true;

                // 展示按钮
                showOption.value = true;
                isChoose.value = true;
                selectData.value.markTimeTs = currentTime.value;
                // 选择时间--开始计时
                roundCountdown?.start({
                    key: 'roundCountdown',
                    finishTime: (t) => t + roundTime,
                    onFinished: () => {
                        // 设置超时时间
                        emits('roundOver', {
                            ...selectData.value,
                            timeoutTs: currentTime.value,
                        });
                        isChoose.value = false; // 选择时间到，禁止选择
                        useNotificationTips.open({
                            type: 'tips-message',
                            text: '作答超时',
                            time: 1000,
                            onClose: () => {
                                roundPassed.value += 1;
                                roundContinue();
                            },
                        });
                    },
                });
            },
        });
    });
}

// 所有回合结束 游戏结束
function gameOver() {
    // 模拟模式下不需要走游戏结束弹窗
    if (!props.isSimulate) {
        useNotificationTips.open({
            type: 'template-message',
            action: 'fruit-view',
            number: goldNumber.num,
            onNext: () => {
                emits('gameOver');
            },
        });
    } else {
        emits('gameOver');
    }
}

// 设置回合默认数据
function setRoundData(index: number = -1) {
    const roundPassedIndex = index > -1 ? index : roundPassed.value;
    const roundData = roundList.value[roundPassedIndex];

    selectData.value = {
        ...defaultSelectData(),
        showTimeTs: currentTime.value, // 设置回合曝光时间
        encSnapshotId: roundData.encryptId, // 回合ID
    };

    const nweFruitList = defineFruitList();

    // 处理回合展示数据
    roundData.paramList.forEach((item) => {
        const { paramIndex, paramId } = item;
        const data = gbaStore.currentQuestion.assetsCongig.find((item) => item.paramId === paramId);
        nweFruitList[paramIndex] = {
            ...item,
            status: 0,
            markedShow: false,
            bgImg: data.paramImg || '',
        };
    });

    // 处理是否按钮数据
    optionList.value = roundData.optionList.map((item, index) => ({
        ...item,
        status: 0,
        shortcutKeys: index + 1,
    }));

    // 按钮回复隐藏
    showOption.value = false;

    fruitList.value = [...nweFruitList];
}

onMounted(() => {
    // 查看是否还有未答回合
    const index = roundList.value.findIndex((item) => item.answerStatus === 0);

    // 计算已答回合金额
    const number = sum(roundList.value.map((item) => item.reward));
    goldNumber.num = number || 0;

    // 如果所有回合回答完毕 直接展示结算弹窗
    if (index < 0) {
        roundPassed.value = roundList.value.length;
        setRoundData(roundList.value.length - 1);
        gameOver();
    } else {
        roundPassed.value = index;
        setRoundData();
        useNotificationTips.open({
            type: 'countdown-start-message',
            time: 3500,
            onClose: () => {
                roundContinue();
            },
        });
    }
});
</script>

<style lang="less" scoped>
.boll {
    position: fixed;
    width: 10px;
    height: 10px;
    border-radius: 10px;
    background: red;
    z-index: 9999;
}

.round-content {
    position: relative;
    padding: 30px 32px;
    width: 100%;
    height: 100%;
    overflow: hidden;

    background: linear-gradient(179.99998deg, #44dbfe 0%, #cdf3e9 47%, #82fdb1 95%);
    box-shadow: inset 0px 4px 35px 0px rgba(16, 185, 239, 0.3);
    backdrop-filter: blur(18.5px);

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 80px;
        width: 100%;
        border: 1px solid red;
        background: linear-gradient(179.99998deg, rgba(227, 255, 250, 0.4) 0%, rgba(235, 255, 247, 0.4) 100%);
        filter: blur(25px);
    }

    &::after {
        content: '';
        position: absolute;
        left: 0;
        height: 261px;
        right: 0;
        bottom: -5px;
        background-image: url('https://img.bosszhipin.com/static/file/2024/h6xy4k3ehq1725606964354.png.webp');
        background-size: 100% auto;
        z-index: -1;
    }

    .title-wrap {
        position: relative;
        height: 38px;
        display: flex;
        justify-content: space-between;

        .title {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: #006180;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
        }

        .progress-wrap {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
        }

        .gold-wrap {
            position: absolute;
            right: 0;

            :deep(.button-wrap) {
                width: 150px;

                .text {
                    margin-left: 10px;
                }
            }

            .gold-icon {
                position: absolute;
                left: -9px;
                width: 38px;
                height: 38px;
                background-image: url(https://img.bosszhipin.com/static/file/2024/mn0equr2ru1725349213782.png.webp);
                background-position: center;
                background-size: 72px;
                z-index: 4;
            }
        }
    }

    .mine-wrap {
        margin-top: 46px;
        padding: 0 23px;
        display: flex;
        justify-content: space-between;

        .mine-item {
            position: relative;
            display: flex;
            justify-content: center;
            width: 142px;
            height: 142px;
            // border: 5px solid #EEFDFF;
            //
            background: #fff;
            padding: 5px;
            border-radius: 22px;
            // overflow: hidden;

            &::after {
                content: '';
                position: absolute;
                left: 50%;
                top: 153px;
                // opacity: 0.20;
                width: 80px;
                height: 18px;
                transform: translateX(-50%);
                background: #000;
                background: radial-gradient(#000 0%, rgba(0, 0, 0, 0) 100%);
                border-radius: 100%;
                opacity: 0.07;
                filter: blur(4px);
            }

            .mine-item-inner {
                width: 100%;
                height: 100%;
                border-radius: 17px;
                overflow: hidden;
                background: linear-gradient(179.99998deg, #42cdf8 0%, #c6f6ff 100%);
            }

            &.marked {
                position: relative;

                .marked-tag-wrap {
                    position: absolute;
                    top: 0;
                    width: 100px;
                    height: 25px;
                    overflow: hidden;

                    .marked-tag {
                        transform: translateY(-100%);
                        width: 100%;
                        height: 100%;
                        background-image: url('https://img.bosszhipin.com/static/file/2024/6j39fsi5pp1726652537124.png.webp');
                        background-size: 100% auto;
                        background-position: center -3px;
                        transition: top 0.2s;

                        &.show {
                            animation: markedTagShow 0.3s 0.2s forwards;
                        }
                    }
                }
            }

            // 收益结果
            .result-info-wrap {
                min-width: 67px;
                height: 36px;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%);

                overflow: hidden;

                .result-info-inner {
                    padding: 7px 10px 0;
                    width: 100%;
                    height: 100%;
                    background: #ffffff;
                    border-radius: 10px;
                    animation: rotateIn 0.3s forwards;

                    &.error {
                        padding: 7px 10px;
                        width: 96px;
                        background: #ffd9d9;
                        backdrop-filter: blur(2px);
                        border: 1px solid #ff7f7f;
                        display: flex;
                        align-items: center;

                        .text {
                            font-size: 14px;
                            color: #db0000;
                            white-space: nowrap;
                        }

                        .error-icon {
                            display: block;
                            margin-right: 4px;
                            width: 16px;
                            height: 16px;
                            background: url(https://img.bosszhipin.com/static/file/2024/mgdbnmslt81725873396289.png.webp) no-repeat;
                            background-size: auto 100%;
                        }
                    }

                    .error-icon {
                        display: none;
                    }

                    .text {
                        color: #338c00;
                        font-size: 15px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: normal;
                        text-align: center;
                    }
                }
            }
        }
    }

    .optin-button-wrap {
        margin-top: 34px;
        overflow: hidden;

        .optin-button-inner {
            display: flex;
            justify-content: center;
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.3s;

            &.show-option {
                transform: translateY(0);
                opacity: 1;
            }

            .button-inner {
                width: 112px;

                &:first-child {
                    margin-right: 40px;
                }

                .button-inner-text {
                    .icon {
                        width: 20px;
                        height: 20px;
                        background-size: 100% auto;
                        &.green-tick {
                            background-image: url(https://img.bosszhipin.com/static/file/2024/34hjehwz2k1726036366579.png.webp);
                        }

                        &.orange-tick {
                            background-image: url(https://img.bosszhipin.com/static/file/2024/g8xr90q1k81726036367212.png.webp);
                        }

                        &.cross {
                            background-image: url(https://img.bosszhipin.com/static/file/2024/kvfc7nvx351726036366218.png.webp);
                        }
                    }
                }

                .key-down-desc {
                    opacity: 0.54;
                    margin-top: 8px;
                    text-align: center;
                    color: #000;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                }
            }
        }
    }

    .text-icon {
        border: 1px solid red;
        background: red;
    }
}

@keyframes rotateIn {
    from {
        transform: rotateY(-180deg);
        opacity: 0;
    }
    to {
        transform: rotateY(0deg);
        opacity: 1;
    }
}

@keyframes markedTagShow {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}
</style>
