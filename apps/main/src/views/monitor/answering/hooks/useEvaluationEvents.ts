import { ref, nextTick } from 'vue';

interface IParams {
    seqId: string;
    examId: string;
}

interface IResult {
    code: number;
    data: any;
}

export function useEvaluationEvents({ examId, seqId }: IParams, onSuccess?: (result: IResult) => void, onError?: (result: IResult) => void) {
    const lastQuestionId = ref('');

    function onQuestionChange(data: { type?: string; currentQuestionId: string }) {
        const fromId = lastQuestionId.value || '';
        const toId = data.type === 'end' ? '' : data.currentQuestionId;
        if (fromId || toId) {
            // @ts-ignore
            postLog('eval_question_change', {
                encExamId: examId,
                p2: '',
                p3: lastQuestionId.value,
                p4: toId,
            });
        }

        nextTick(() => {
            lastQuestionId.value = toId;
        });
    }

    return {
        onQuestionChange,
        lastQuestionId, // 也导出 lastQuestionId 供外部潜在需要
    };
}
