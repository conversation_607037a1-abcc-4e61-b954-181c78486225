<template>
    <slot />
    <div class="container">
        <div class="header">
            <div style="width: 83px">方案</div>
            <div style="width: 139px">参数</div>
            <div style="flex: 1">结果</div>
        </div>
        <TransitionGroup name="list" tag="div" class="checkbox-group">
            <b-checkbox
                v-for="(plan, index) in hotsStore.currentHistoryScheme"
                :key="plan.encryptId"
                class="checkbox"
                :modelValue="checked.includes(plan.encryptId)"
                style="margin: 0"
                @click.prevent="handleToggleCheck(plan.encryptId, index)"
            >
                <template #checkbox="{ checked }">
                    <div class="item">
                        <div style="width: 83px; display: flex; align-items: start">
                            <b-checkbox :checked="checked" style="margin-top: 1px; margin-right: 6px" />
                            <div>{{ plan.num }}</div>
                        </div>
                        <div style="width: 139px; flex-direction: row; display: flex">
                            <div style="flex-direction: column; display: flex; gap: 6px">
                                <div v-for="(item, index) in plan.input" :key="index" style="color: #808080; white-space: nowrap">{{ item.showName }}：</div>
                            </div>
                            <div style="width: 139px; flex-direction: column; display: flex; gap: 6px">
                                <div v-for="(item, index) in plan.input" :key="index" class="number">
                                    <span class="number">{{ item.paramValue }}</span>
                                </div>
                            </div>
                        </div>

                        <div style="flex-direction: row; display: flex">
                            <div style="flex-direction: column; display: flex; gap: 6px">
                                <div v-for="(item, index) in plan.output" :key="index" style="color: #808080; white-space: nowrap">{{ item.showName }}：</div>
                            </div>
                            <div style="flex-direction: column; display: flex; gap: 6px">
                                <div v-for="(item, index) in plan.output" :key="index" class="number">
                                    <span class="number">{{ item.paramValue }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </b-checkbox>
        </TransitionGroup>
        <div v-if="hotsStore.currentHistoryScheme.length === 0" style="line-height: 40px; color: #808080; width: 100%; text-align: center">暂无数据</div>
    </div>
</template>

<script setup lang="ts" name="PlanQuestion">
import { ref, watchEffect } from 'vue';
import { debounce } from 'lodash-es';
import { useHotsStore } from '../store';

const $emit = defineEmits(['onAnswerChange']);

const hotsStore = useHotsStore();

const getAnswerData = () => checked.value;

const checked = ref<string[]>([]);

let flag = false;
watchEffect(() => {
    if (!flag && hotsStore.answerData[hotsStore.currentQuestion.encryptId]) {
        flag = true;
        checked.value = JSON.parse(hotsStore.answerData[hotsStore.currentQuestion.encryptId] || {});
    }
});

function handleToggleCheck(encryptId: string, index: number) {
    if (checked.value.includes(encryptId)) {
        checked.value = checked.value.filter((item) => item !== encryptId);
    } else {
        checked.value.push(encryptId);
    }
    debounceHandle();
}
const debounceHandle = debounce(() => {
    $emit('onAnswerChange', getAnswerData());
}, 500);

function validate() {
    if (checked.value.length === 0) {
        hotsStore.answerToast.open({
            status: 'warning',
            title: '请至少选择一个方案',
        });
        return false;
    } else {
        return true;
    }
}

defineExpose({
    getAnswerData,
    validate,
});
</script>

<style lang="less" scoped>
.list-enter-active,
.list-leave-active {
    transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

.container {
    display: flex;
    flex-direction: column;
    gap: 6px;
    overflow: hidden;
}

.header {
    display: flex;
    font-size: 14px;
    line-height: 16px;
    color: #1f1f1f;
    background: #f7f7f7;
    padding: 12px;
}

.checkbox {
    margin: 0;
    :hover {
        background: #e7f9f9;
    }
}

.item {
    display: flex;
    flex-direction: row;
    font-size: 14px;
    line-height: 16px;
    color: #1f1f1f;
    padding: 12px;
    border: 1px solid #dfdfdf;
    border-radius: 6px;
    width: 100%;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 100%;
}

.number {
    color: #1f1f1f;
    font-family: kanzhun;
}
</style>
