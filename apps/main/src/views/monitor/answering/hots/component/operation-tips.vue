<template>
    <div class="operation-tips">
        <template v-if="stepType === 'guide'">
            <p class="title">操作指引</p>
            <p class="desc">
                本测验采用场景+小题目的形式，左侧为场景区，你可在此区域进行反复探索互动来获取解答问题的信息。右侧为答题区，题目有连线题、方案题两种题型。过程中会出现弹窗信息提示，按提示操作即可。
            </p>
            <div class="content">
                <RichText :html="examInfo?.answerInstruction" />
            </div>
        </template>
        <template v-if="stepType === 'illustrate'">
            <p class="title">作答说明</p>
            <p class="time-info">
                <span>共{{ examInfo?.questionCount }}题</span>
                <em class="line" />
                <span>限时{{ examInfo?.adviseDurationStr }}</span>
            </p>
            <div class="content">
                <RichText :html="examInfo?.operateGuide" />
            </div>
        </template>
        <div class="options-btn">
            <b-button v-if="stepType === 'guide'" class="next-btn" type="primary" shape="round" @click="changeStep('next')"> 下一步 </b-button>
            <template v-if="stepType === 'illustrate'">
                <b-button class="next-btn" type="outline" shape="round" @click="changeStep('prev')"> 上一步 </b-button>
                <b-button class="next-btn" type="primary" shape="round" @click="changeStep('over')"> 阅读完毕，开始答题 </b-button>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import RichText from '@/components/rich-text/index.vue';
import { useMonitorStore } from '@/store/use-monitor-store';

defineProps({
    isPreview: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(['onOver']);
const monitorStore = useMonitorStore();
const { examBaseInfo } = monitorStore;

const examInfo = computed(() => examBaseInfo.examInfo);
const stepList = ['guide', 'illustrate'];
const stepIndex = ref(0);
const stepType = computed(() => {
    return stepList[stepIndex.value];
});

function changeStep(type: string) {
    if (type === 'next') {
        stepIndex.value += 1;
    }

    if (type === 'prev') {
        stepIndex.value -= 1;
    }

    if (type === 'over') {
        emits('onOver');
    }
}
</script>

<style lang="less">
@import '@/styles/ui-reset/rich-text.less';
</style>

<style lang="less" scoped>
.operation-tips {
    padding: 32px 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(160.13423deg, #f4f7f9 4%, #ffffff 48%);
    box-shadow:
        4px 4px 10px 0px rgba(34, 41, 43, 0.1),
        -5px -4px 14px 0px #ffffff;
    border-radius: 7px;

    .title {
        padding: 0 0 24px;
        color: #1f1f1f;
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px;
        text-align: center;
    }

    .desc {
        padding: 0 32px;
        color: #4d4d4d;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
    }

    .time-info {
        padding: 0 32px;
        color: #1f1f1f;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        display: flex;
        align-items: center;

        .line {
            margin: 0 8px;
            width: 1px;
            height: 11px;
            background: #ececec;
        }
    }

    .tips-img {
        padding: 20px;
        margin-top: 12px;
        flex: 1;
        background: #f7f8fb;
        border-radius: 4px;
        overflow-y: scroll;
    }

    .options-btn {
        margin-top: 24px;
        display: flex;
        justify-content: center;

        .next-btn {
            padding: 8px 27px;

            & + .next-btn {
                margin-left: 16px;
            }
        }
    }

    .content {
        padding: 0 32px;
        margin-top: 12px;
        flex: 1;
        overflow-y: scroll;
    }
}
</style>
