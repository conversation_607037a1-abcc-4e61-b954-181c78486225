import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMonitorStore } from '@/store/use-monitor-store';
import { Toast } from '@boss/design';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';
import { ExamStatusEnum } from '@crm/exam-types';
import { logger } from '@/utils/logger';

export enum AnswerStatusEnum {
    开始作答 = 1,
    已完成 = 2,
    已结束 = 3,
}

export interface IExamItem {
    encryptExamId: string; // 加密考试id
    examName: string; // 考试名称
    startTime: string; // 考试开始时间
    endTime: string; // 考试结束时间
    examSource: 0 | 2; // 关联来源 0考试 2测评
    sort: number; // 排序 从1开始
    answerTypes: (1 | 2)[]; // 作答方式 1电脑端 2手机端
    examUrl: string; // 考试链接或二维码
    answerStatus: AnswerStatusEnum; // 作答状态 1开始作答  2已完成 3已结束
    adviseDurationStr?: string; // 作答时间
    examH5Url?: string; // 考试H5链接
    combinationType: 1 | 2; // 组合类型 1 按场次时间开始 2 前场交卷即可开始
    subExamList: IExamItem[];
}

export function useExamList({ seqId }: { seqId: string }) {
    const list = ref<IExamItem[]>([]);
    const includePhoneAnswer = ref(false);

    const router = useRouter();
    const monitorStore = useMonitorStore();

    const fetchExamList = async () => {
        try {
            const res = await Invoke.examList.getExamList({
                encryptExamId: seqId,
            });

            if (res.code === 0) {
                const { data } = res;
                list.value = data.examList.map((item: IExamItem) => {
                    const { subExamList } = item;
                    return {
                        ...item,
                        subExamList: subExamList && subExamList.length ? subExamList : [item],
                    };
                });
                includePhoneAnswer.value = data.includePhoneAnswer;
                monitorStore.setDebugInfo(data.debugInfo);
            } else {
                Toast.danger(`${res.message}`);
            }

            return res;
        } catch (error: any) {
            Toast.danger(`${error.message}`);
        }
    };

    async function navigateToDebug() {
        // 获取基础信息
        const { code, data } = await fetchExamList();

        if (code === 0) {
            const { canDebug } = data.debugInfo;
            if (canDebug) {
                // 执行路由跳转至调试页面
                router.replace(`/monitor?seqId=${seqId}`);
            } else {
                // 路由跳转至调试已结束状态页面
                router.replace(`/status/${seqId}?status=${ExamStatusEnum.调试已结束}&text=考前设备调试已结束`);
            }
        }
    }

    return {
        list,
        includePhoneAnswer,
        fetchExamList,
        navigateToDebug,
    };
}
