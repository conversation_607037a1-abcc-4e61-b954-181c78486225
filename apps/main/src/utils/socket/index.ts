import { urls } from '@/shared/url';
import { emitter, md5, protobufDecode, protobufEncode } from '@crm/exam-utils';
import * as mqtt from 'mqtt';

const maxReconnect = 20;
export const chatService = {
    client: {
        on(message: string, param2: (topic: string, message: any) => void) {
            logger.info(message, param2, message);
        },
        end() {},
        publish(topic: string, payload: any, param3: object, param4: (error: any) => void) {
            logger.info(topic, payload, param3, param4);
        },
    },
    rec_time: 0,

    // 上线
    connect(params: any) {
        const parmasToSend = {
            keepalive: 30, // 心跳
            username: `${params?.encryptMobile}|1`, // 用户名
            password: md5.hex_md5(`ws-${params?.examId}${params?.encryptMobile}|1${params.wsConnectSecrect}`), // 密码
            clientId: `ws-${params?.examId}`, // 客户端id 标识客户端的身份
            reconnectPeriod: 1000, // (毫秒) 两次重新连接之间的间隔
            connectTimeout: 30 * 1000, // 设置超时时间
        };
        this.client = mqtt.connect(urls.wsUrl, parmasToSend);

        // 收到消息
        this.client.on('message', (topic, message) => {
            // console.info(protobuf.protobufDecode(message));
            const decodedMessage = protobufDecode(message);
            emitter.emit('receiveMessage', decodedMessage);
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-examinee-receive-message', {
                pData: {
                    type: TrackTypeEnum.成功,
                    message: `消息类型为${decodedMessage?.proType}, 消息ID为${decodedMessage?.messages?.[0]?.messageId}`,
                    reportType: ReportTypeEnum.ws,
                    nameZh: '考生收到消息',
                },
            });
            // 埋点结束
        });

        // 断线重连事件
        this.client.on('reconnect', () => {
            ++this.rec_time; // 自动重连次数加1
            if (this.rec_time > maxReconnect) {
                // 超过最大连接数触发
                this.client.end();
            }
            logger.debug(this.rec_time, 'chatService 断线重连事件');
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-ws-reconnect', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.ws,
                    nameZh: 'ws断线重连',
                },
            });
            // 埋点结束
        });

        // 自动重连，成功后触发
        this.client.on('connect', () => {
            this.rec_time = 0; // 链接成功，自动重连次数清零
            logger.info('chatService 连接事件');
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-ws-connect', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.ws,
                    nameZh: 'ws连接',
                },
            });
            // 埋点结束
        });

        // 当客户端无法连接或发生解析错误时回调
        this.client.on('error', () => {
            // 关闭客户端（断开连接）
            this.client.end();
            logger.info('chatService 关闭客户端（断开连接）');
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-ws-error', {
                pData: {
                    type: TrackTypeEnum.失败,
                    reportType: ReportTypeEnum.ws,
                },
            });
            // 埋点结束
        });

        // 断开连接后回调
        this.client.on('close', () => {
            logger.debug('chatService 断开连接后回调');
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-ws-close', {
                pData: {
                    type: TrackTypeEnum.失败,
                    reportType: ReportTypeEnum.ws,
                    nameZh: 'ws断开连接',
                },
            });
            // 埋点结束
        });

        // 当客户端离线时回调
        this.client.on('offline', () => {
            logger.info('chatService 当客户端离线时回调');
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-ws-offline', {
                pData: {
                    type: TrackTypeEnum.失败,
                    reportType: ReportTypeEnum.ws,
                    nameZh: 'ws离线',
                },
            });
            // 埋点结束
        });

        // 收到来自代理的断开数据包后回调
        this.client.on('disconnect', () => {
            logger.info('chatService 收到来自代理的断开数据包后回调');
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-ws-disconnect', {
                pData: {
                    type: TrackTypeEnum.成功,
                    reportType: ReportTypeEnum.ws,
                },
            });
            // 埋点结束
        });

        // 当客户端发送任何数据包时回调
        this.client.on('packetsend', (params: any) => {
            // console.info(params.payload, '当客户端发送任何数据包时回调');
        });

        // 当客户端收到任何数据包时回调
        this.client.on('packetreceive', (topic, payload) => {
            // console.info(topic, payload, '当客户端收到任何数据包时回调');
        });
    },
    disconnect() {
        this.client.end();
    },
    sendMessage(currentParams: any, currentTopic = 'chat') {
        const payload = {
            messages: [
                {
                    text: currentParams.content,
                    examId: currentParams.examId,
                    groupId: currentParams.groupId,
                    messageType: 1,
                    to: {
                        userId: '0',
                        roleType: 2,
                    },
                    from: {
                        userId: currentParams.userId,
                        userName: currentParams.userName,
                        roleType: 1,
                    },
                },
            ],
            proType: 1,
        };

        if (this.client) {
            this.client.publish(currentTopic, protobufEncode(payload), {}, (error) => {
                if (error) {
                    logger.error('MQTT publish error', error);
                } else {
                    emitter.emit('sendMessage', payload);
                }
            });
        }
    },
};
