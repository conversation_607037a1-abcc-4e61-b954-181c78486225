<template>
    <div v-if="visible" ref="el" :style="style" class="content-image">
        <div ref="handle" class="header">
            <div />
            <div class="img-opts">
                <div class="opt-btn" @click="enlargeImg">
                    <svg viewBox="0 0 24 24" width="24px" height="24px">
                        <g fill="none" fill-rule="evenodd">
                            <circle cx="12" cy="12" r="12" />
                            <g stroke="currentColor" stroke-linecap="round" stroke-width="1.5">
                                <path d="M11.5 20a8.5 8.5 0 1 0 0-17 8.5 8.5 0 0 0 0 17ZM11.5 8.5v6M8.508 11.508 14.5 11.5M17.611 17.611l4.242 4.243" />
                            </g>
                        </g>
                    </svg>
                </div>
                <div class="opt-btn" @click="shrinkImg">
                    <svg viewBox="0 0 24 24" width="24px" height="24px">
                        <g fill="none" fill-rule="evenodd">
                            <circle cx="12" cy="12" r="12" />
                            <g stroke="currentColor" stroke-linecap="round" stroke-width="1.5">
                                <path d="M11.5 20a8.5 8.5 0 1 0 0-17 8.5 8.5 0 0 0 0 17ZM8.5 11.5h6M17.611 17.611l4.242 4.243" />
                            </g>
                        </g>
                    </svg>
                </div>
                <div class="init opt-btn" @click="initImg">
                    <svg viewBox="0 0 1025 1024" width="24" height="24">
                        <path
                            d="M951.637 998.059h-868.898c-39.322 0-70.997-31.676-70.997-70.997v-833.946c0-39.322 31.676-70.997 70.997-70.997h868.898c39.322 0 70.997 31.676 70.997 70.997v833.946c0 39.322-31.676 70.997-70.997 70.997zM82.739 77.824c-8.192 0-15.292 7.1-15.292 15.292v833.946c0 8.192 7.1 15.292 15.292 15.292h868.898c8.192 0 15.292-7.1 15.292-15.292v-833.946c0-8.192-7.1-15.292-15.292-15.292h-868.898z"
                            fill=""
                            p-id="12901"
                        />
                        <path
                            d="M307.2 262.963v440.183h-58.982v-312.388h-102.673v-43.691c38.229 0 64.444-4.915 79.735-14.199 16.93-11.469 30.037-34.406 38.775-69.359h43.145zM550.229 380.382v66.082h-65.536v-66.082h65.536zM550.229 637.065v66.082h-65.536v-66.628h65.536zM829.303 262.963v440.183h-58.982v-312.388h-102.673v-43.691c38.229 0 64.444-4.915 79.735-14.199 16.93-11.469 30.037-34.406 38.775-69.359h43.145z"
                            fill=""
                            p-id="12902"
                        />
                    </svg>
                </div>
            </div>
            <span class="close-image" @click="closePreview">
                <svg viewBox="0 0 1024 1024" width="24" height="24">
                    <path
                        d="M576 512l277.333333 277.333333-64 64-277.333333-277.333333L234.666667 853.333333 170.666667 789.333333l277.333333-277.333333L170.666667 234.666667 234.666667 170.666667l277.333333 277.333333L789.333333 170.666667 853.333333 234.666667 576 512z"
                        fill="currentColor"
                    />
                </svg>
            </span>
        </div>
        <div :key="key" v-loading="loading" class="viewer-img">
            <Viewer ref="viewer" :images="viewerList" :options="options" rebuild hidden @inited="viewerInited">
                <img v-for="src in viewerList" :key="src" :src="src" @load="handleImgLoad" />
            </Viewer>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useDraggable } from '@crm/vueuse-pro';
import { component as Viewer } from 'v-viewer';
import { ref } from 'vue';
// import 'viewerjs/dist/viewer.css';

defineOptions({
    name: 'PreviewImage',
});

const visible = ref(false);

const $viewer = ref<any>({});
const viewerList = ref<string[]>();
const el = ref();
const handle = ref();
const { style } = useDraggable(el, {
    handle,
    containerElement: document.documentElement,
    preventDefault: true,
    initialValue: { x: 500, y: 100 },
});

const key = ref(0);

function viewerInited(viewer: any) {
    $viewer.value = viewer;
}
const options = ref({
    inline: true,
    title: false,
    navbar: false,
    fullscreen: false,
    toolbar: false,
});
function enlargeImg() {
    $viewer.value?.zoom(0.25);
}
function shrinkImg() {
    $viewer.value?.zoom(-0.25);
}
function initImg() {
    key.value += 1;
}
const loading = ref(false);
function handleImgLoad() {
    loading.value = false;
}
function openPreview(src: string) {
    visible.value = true;
    viewerList.value = [src];
    loading.value = true;
}

function closePreview() {
    visible.value = false;
    viewerList.value = [];
}

defineExpose({
    open: openPreview,
    close: closePreview,
});
</script>

<style lang="less" scoped>
.content-image {
    position: fixed !important;
    background: #f7f7f7;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    border: none !important;
    cursor: move;
    z-index: 9999;
    border-radius: 12px 12px 0 0;
    width: 680px;
    height: 544px;
    // left: 500px;
    // top: 100px;

    :deep(.vdr-handle) {
        // display: none !important;
        opacity: 0 !important;
    }

    .header {
        border-radius: 12px 12px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f2f2f2;
        border-bottom: 1px solid #ececec;
        padding: 10px;

        .img-opts {
            display: flex;
            justify-content: space-evenly;
            width: 340px;

            .opt-btn {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                cursor: pointer;
                color: #29292d;

                &:hover {
                    background: #dfdfdf;
                }

                .svg-icon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translateX(-50%) translateY(-50%);
                }
            }
        }
        .close-image {
            margin: 0 12px 0 0;
            cursor: pointer;
            color: #29292d;
            display: flex;
            align-items: center;
        }
    }

    .image-box {
        display: flex;
        height: calc(~'100% - 106px');
        background: #f4f7f9;
        text-align: center;
        border-radius: 8px;

        img {
            max-width: 384px;
            max-height: 246px;
            margin: auto;
        }
    }

    // .footer {
    //     height: 8px;
    //     width: 100%;
    //     border-radius: 0 0 8px 8px;
    //     background: #F7F7F7;
    // }
}
:deep(.viewer-img) {
    height: calc(100% - 26px);
    // :deep(*) {
    .viewer-backdrop {
        height: 100% !important;
        background: #f4f7f9;
        text-align: center;
        border-radius: 0 0 8px 8px;
    }
    .viewer-fullscreen {
        display: none;
    }
    // }
}
</style>
